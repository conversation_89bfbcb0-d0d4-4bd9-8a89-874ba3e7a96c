import React, { useState, useEffect, useRef } from 'react'
import { useTheme, themes, fontSizes } from '../../context/ThemeContext'
import { HiColorSwatch, HiCheck, HiChevronDown, HiOutlineAdjustments } from 'react-icons/hi'

function ThemeSelector() {
  const [themeDropdownOpen, setThemeDropdownOpen] = useState(false)
  const [fontSizeDropdownOpen, setFontSizeDropdownOpen] = useState(false)
  const themeDropdownRef = useRef(null)
  const fontSizeDropdownRef = useRef(null)

  const {
    currentTheme,
    setCurrentTheme,
    theme,
    currentFontSize,
    setCurrentFontSize,
    fontSize
  } = useTheme()

  // Helper function to get appropriate border colors for dark mode
  const getBorderColor = (isSelected) => {
    if (isSelected) {
      return `${theme.border} ring-2 ring-opacity-50`
    }
    return currentTheme === 'dark' ? 'border-gray-600 hover:border-gray-500' : 'border-gray-200 hover:border-gray-300'
  }

  // Close dropdowns when clicking outside
  useEffect(() => {
    function handleClickOutside(event) {
      if (themeDropdownRef.current && !themeDropdownRef.current.contains(event.target)) {
        setThemeDropdownOpen(false)
      }
      if (fontSizeDropdownRef.current && !fontSizeDropdownRef.current.contains(event.target)) {
        setFontSizeDropdownOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  return (
    <div className="space-y-8">
      {/* Theme Selection */}
      <div>
        <h3 className={`text-lg font-semibold ${theme.text} mb-4 flex items-center`}>
          <HiColorSwatch className="mr-2 h-5 w-5" />
          Choose Theme
        </h3>
        <div className="relative" ref={themeDropdownRef}>
          <button
            onClick={() => setThemeDropdownOpen(!themeDropdownOpen)}
            className={`w-full p-4 ${theme.card} border ${theme.border} rounded-lg flex items-center justify-between hover:${theme.secondary} transition-colors`}
          >
            <div className="flex items-center space-x-4">
              {/* Current theme preview */}
              <div className="space-y-2">
                <div className={`h-6 w-16 ${themes[currentTheme].gradient} bg-gradient-to-r rounded`} />
                <div className="flex space-x-1">
                  <div className={`h-2 w-2 ${themes[currentTheme].primary} rounded`} />
                  <div className={`h-2 w-2 ${themes[currentTheme].accent} rounded`} />
                  <div className={`h-2 w-2 ${themes[currentTheme].secondary} rounded`} />
                </div>
              </div>
              <span className={`text-lg font-medium ${theme.text}`}>
                {themes[currentTheme].name}
              </span>
            </div>
            <HiChevronDown
              className={`h-5 w-5 ${theme.textSecondary} transition-transform ${
                themeDropdownOpen ? 'rotate-180' : ''
              }`}
            />
          </button>

          {themeDropdownOpen && (
            <div className={`absolute top-full left-0 right-0 mt-2 ${theme.card} border ${theme.border} rounded-lg shadow-lg z-50 max-h-80 overflow-y-auto`}>
              <div className="grid grid-cols-1 gap-2 p-2">
                {Object.entries(themes).map(([themeKey, themeData]) => (
                  <button
                    key={themeKey}
                    onClick={() => {
                      setCurrentTheme(themeKey)
                      setThemeDropdownOpen(false)
                    }}
                    className={`relative p-4 rounded-lg border-2 transition-all hover:scale-105 text-left ${getBorderColor(currentTheme === themeKey)}`}
                  >
                    <div className="flex items-center space-x-4">
                      {/* Theme preview */}
                      <div className="space-y-2 flex-shrink-0">
                        <div className={`h-8 w-20 ${themeData.gradient} bg-gradient-to-r rounded`} />
                        <div className="flex space-x-1">
                          <div className={`h-3 w-3 ${themeData.primary} rounded`} />
                          <div className={`h-3 w-3 ${themeData.accent} rounded`} />
                          <div className={`h-3 w-3 ${themeData.secondary} rounded`} />
                        </div>
                      </div>

                      {/* Theme name */}
                      <div className="flex-1">
                        <p className={`text-sm font-medium ${theme.text}`}>
                          {themeData.name}
                        </p>
                      </div>

                      {/* Selected indicator */}
                      {currentTheme === themeKey && (
                        <div className="flex-shrink-0">
                          <HiCheck className="h-5 w-5 text-blue-500" />
                        </div>
                      )}
                    </div>
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Font Size Selection */}
      <div>
        <h3 className={`text-lg font-semibold ${theme.text} mb-4 flex items-center`}>
          <HiOutlineAdjustments className="mr-2 h-5 w-5" />
          Font Size
        </h3>
        <div className="relative" ref={fontSizeDropdownRef}>
          <button
            onClick={() => setFontSizeDropdownOpen(!fontSizeDropdownOpen)}
            className={`w-full p-4 ${theme.card} border ${theme.border} rounded-lg flex items-center justify-between hover:${theme.secondary} transition-colors`}
          >
            <div className="flex items-center space-x-4">
              {/* Current font size preview */}
              <div className="space-y-2">
                <div className={`${fontSize.scale} font-medium ${theme.text}`}>
                  Sample Text
                </div>
                <div className={`text-xs ${theme.textSecondary}`}>
                  {fontSize.name} ({fontSize.size})
                </div>
              </div>
            </div>
            <HiChevronDown className={`h-5 w-5 ${theme.textSecondary} transition-transform ${fontSizeDropdownOpen ? 'rotate-180' : ''}`} />
          </button>

          {fontSizeDropdownOpen && (
            <div className={`absolute top-full left-0 right-0 mt-2 ${theme.card} border ${theme.border} rounded-lg shadow-lg z-50 max-h-80 overflow-y-auto`}>
              <div className="grid grid-cols-1 gap-2 p-2">
                {Object.entries(fontSizes).map(([sizeKey, sizeData]) => (
                  <button
                    key={sizeKey}
                    onClick={() => {
                      setCurrentFontSize(sizeKey)
                      setFontSizeDropdownOpen(false)
                    }}
                    className={`relative p-4 rounded-lg border-2 transition-all hover:scale-105 text-left ${getBorderColor(currentFontSize === sizeKey)}`}
                  >
                    <div className="flex items-center justify-between">
                      {/* Font size preview */}
                      <div className="flex-1">
                        <div className={`${sizeData.scale} font-medium ${theme.text} mb-1`}>
                          Sample Text
                        </div>
                        <p className={`text-xs ${theme.textSecondary}`}>
                          {sizeData.name} ({sizeData.size})
                        </p>
                      </div>

                      {/* Selected indicator */}
                      {currentFontSize === sizeKey && (
                        <div className="flex-shrink-0 ml-4">
                          <HiCheck className="h-5 w-5 text-blue-500" />
                        </div>
                      )}
                    </div>
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Theme & Font Preview */}
      <div>
        <h3 className={`text-lg font-semibold ${theme.text} mb-4`}>
          Preview
        </h3>
        <div className={`p-6 ${theme.card} rounded-lg border ${theme.border} space-y-4`} style={{ fontSize: fontSize.size }}>
          <div className="flex items-center justify-between">
            <h4 className={`${fontSize.headingScale} font-bold ${theme.text}`}>
              Sample Dashboard
            </h4>
            <div className={`px-3 py-1 ${theme.primary} text-white rounded-full ${fontSize.scale}`}>
              Active
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className={`p-4 ${theme.secondary} rounded-lg`}>
              <p className={`${fontSize.scale} ${theme.textSecondary}`}>Current Units</p>
              <p className={`${fontSize.headingScale} font-bold ${theme.text}`}>125.50</p>
            </div>
            <div className={`p-4 ${theme.secondary} rounded-lg`}>
              <p className={`${fontSize.scale} ${theme.textSecondary}`}>Usage Today</p>
              <p className={`${fontSize.headingScale} font-bold ${theme.text}`}>8.25</p>
            </div>
            <div className={`p-4 ${theme.secondary} rounded-lg`}>
              <p className={`${fontSize.scale} ${theme.textSecondary}`}>Cost</p>
              <p className={`${fontSize.headingScale} font-bold ${theme.text}`}>R20.63</p>
            </div>
          </div>

          <div className="flex space-x-2">
            <button className={`px-4 py-2 ${theme.primary} text-white rounded-lg ${fontSize.scale}`}>
              Primary Button
            </button>
            <button className={`px-4 py-2 border ${theme.border} ${theme.text} rounded-lg ${fontSize.scale}`}>
              Secondary Button
            </button>
          </div>
        </div>
      </div>

      {/* Reset to Default */}
      <div>
        <h3 className={`text-lg font-semibold ${theme.text} mb-4`}>
          Reset Appearance
        </h3>
        <div className="space-y-3">
          <button
            onClick={() => {
              setCurrentTheme('electric')
              setThemeDropdownOpen(false)
            }}
            className={`w-full px-6 py-3 border ${theme.border} ${theme.text} rounded-lg hover:${theme.secondary} transition-colors`}
          >
            Reset to Default Theme
          </button>
          <button
            onClick={() => {
              setCurrentFontSize('medium')
              setFontSizeDropdownOpen(false)
            }}
            className={`w-full px-6 py-3 border ${theme.border} ${theme.text} rounded-lg hover:${theme.secondary} transition-colors`}
          >
            Reset to Default Font Size
          </button>
          <button
            onClick={() => {
              setCurrentTheme('electric')
              setCurrentFontSize('medium')
              setThemeDropdownOpen(false)
              setFontSizeDropdownOpen(false)
            }}
            className={`w-full px-6 py-3 bg-gradient-to-r ${theme.gradient} text-white rounded-lg hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl`}
          >
            Reset All Appearance Settings
          </button>
        </div>
      </div>
    </div>
  )
}

export default ThemeSelector
