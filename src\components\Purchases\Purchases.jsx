import React, { useState } from 'react'
import { useApp } from '../../context/AppContext'
import { useTheme } from '../../context/ThemeContext'
import PurchaseForm from './PurchaseForm'
import HelpTooltip from '../Common/HelpTooltip'
import { HiCurrencyDollar, HiLightningBolt, HiCalendar } from 'react-icons/hi'

function Purchases() {
  const { state, getDisplayUnitName } = useApp()
  const { theme, currentTheme } = useTheme()

  // Helper function to get appropriate background for cards in dark mode
  const getCardBackground = (lightBg, darkBg = 'bg-gray-800/50') => {
    return currentTheme === 'dark' ? darkBg : lightBg
  }

  const totalSpent = state.purchases.reduce((total, purchase) => total + purchase.currency, 0)
  const totalUnits = state.purchases.reduce((total, purchase) => total + purchase.units, 0)

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div>
        <div className="flex items-center gap-3">
          <h1 className={`text-3xl font-bold ${theme.text}`}>Purchases</h1>
          <HelpTooltip
            title="How Purchases Work"
            content="Enter the amount you want to spend and see exactly how many units you'll receive. The calculation is based on your unit cost setting. All purchases are automatically added to your current units balance and saved to history."
            position="bottom"
          />
        </div>
        <p className={`mt-2 ${theme.textSecondary}`}>
          Add new purchases and view your purchase history
        </p>
      </div>

      {/* Purchase form and recent purchases */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Left Column - Purchase form and summary cards */}
        <div className="space-y-6">
          {/* Purchase form */}
          <div className={`${theme.card} rounded-2xl shadow-lg p-6 border ${theme.border} ${getCardBackground('bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50', 'bg-gray-800/50')}`}>
            <h2 className={`text-xl font-semibold ${theme.text} mb-4 flex items-center gap-3`}>
              <div className="p-2 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 shadow-md">
                <HiCurrencyDollar className="h-5 w-5 text-white" />
              </div>
              Add New Purchase
            </h2>
            <div className={`${getCardBackground('bg-white/60', 'bg-gray-700/50')} backdrop-blur-sm rounded-xl p-4`}>
              <PurchaseForm />
            </div>
          </div>

          {/* Purchase summary cards - stacked vertically */}
          <div className="space-y-3">
            {/* Total Spent Card */}
            <div className={`${theme.card} rounded-xl shadow-lg p-4 border ${theme.border} hover:shadow-xl transition-all duration-300`}>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg bg-gradient-to-br ${theme.gradient} shadow-lg`}>
                    <HiCurrencyDollar className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <p className={`text-sm font-medium ${theme.textSecondary} opacity-80`}>
                      Total Spent
                    </p>
                    <p className={`text-lg font-bold ${theme.text}`}>
                      {state.currencySymbol || 'R'}{totalSpent.toFixed(2)}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-xs text-emerald-500 font-medium">All Purchases</p>
                </div>
              </div>
            </div>

            {/* Total Units Card */}
            <div className={`${theme.card} rounded-xl shadow-lg p-4 border ${theme.border} hover:shadow-xl transition-all duration-300`}>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg bg-gradient-to-br ${theme.gradient} shadow-lg`}>
                    <HiLightningBolt className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <p className={`text-sm font-medium ${theme.textSecondary} opacity-80`}>
                      Total {getDisplayUnitName()} Purchased
                    </p>
                    <p className={`text-lg font-bold ${theme.text}`}>
                      {totalUnits.toFixed(2)}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-xs text-blue-500 font-medium">{getDisplayUnitName()}</p>
                </div>
              </div>
            </div>

            {/* Total Purchases Card */}
            <div className={`${theme.card} rounded-xl shadow-lg p-4 border ${theme.border} hover:shadow-xl transition-all duration-300`}>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg bg-gradient-to-br ${theme.gradient} shadow-lg`}>
                    <HiCalendar className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <p className={`text-sm font-medium ${theme.textSecondary} opacity-80`}>
                      Total Purchases
                    </p>
                    <p className={`text-lg font-bold ${theme.text}`}>
                      {state.purchases.length}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-xs text-violet-500 font-medium">Transactions</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Recent purchases */}
        <div className={`${theme.card} rounded-2xl shadow-lg p-6 border ${theme.border} ${getCardBackground('bg-gradient-to-br from-emerald-50 via-green-50 to-teal-50', 'bg-gray-800/50')}`}>
          <h2 className={`text-xl font-semibold ${theme.text} mb-4 flex items-center gap-3`}>
            <div className="p-2 rounded-xl bg-gradient-to-br from-emerald-500 to-green-600 shadow-md">
              <HiCalendar className="h-5 w-5 text-white" />
            </div>
            Recent Purchases
          </h2>
          <div className="space-y-3 max-h-96 overflow-y-auto">
            {state.purchases.slice(0, 10).map((purchase) => (
              <div
                key={purchase.id}
                className={`p-4 ${getCardBackground('bg-white/60', 'bg-gray-700/50')} backdrop-blur-sm rounded-xl border ${getCardBackground('border-white/40', 'border-gray-600')} shadow-sm hover:shadow-md transition-all duration-200`}
              >
                <div className="flex justify-between items-start">
                  <div>
                    <p className={`font-semibold ${theme.text} text-lg`}>
                      {state.currencySymbol || 'R'}{purchase.currency.toFixed(2)}
                    </p>
                    <p className={`text-sm ${theme.textSecondary} opacity-80`}>
                      {purchase.units.toFixed(2)} {getDisplayUnitName()} @ {state.currencySymbol || 'R'}{purchase.unitCost.toFixed(2)}/{getDisplayUnitName()}
                    </p>
                    <p className={`text-xs ${theme.textSecondary} mt-1 opacity-70`}>
                      {purchase.timestamp}
                    </p>
                  </div>
                  <div className="text-right">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-gradient-to-r from-emerald-100 to-green-100 text-emerald-700 border border-emerald-200`}>
                      +{purchase.units.toFixed(2)} {getDisplayUnitName()}
                    </span>
                    <p className={`text-xs ${theme.textSecondary} mt-1 font-medium`}>
                      {state.currencySymbol || 'R'}{purchase.currency.toFixed(2)}
                    </p>
                  </div>
                </div>
              </div>
            ))}

            {state.purchases.length === 0 && (
              <div className={`text-center py-12 ${getCardBackground('bg-white/40', 'bg-gray-700/40')} backdrop-blur-sm rounded-xl border ${getCardBackground('border-white/40', 'border-gray-600')}`}>
                <div className="p-4 rounded-2xl bg-gradient-to-br from-emerald-100 to-green-200 w-fit mx-auto mb-4">
                  <HiCurrencyDollar className={`h-12 w-12 text-emerald-600`} />
                </div>
                <p className={`text-sm ${theme.textSecondary} opacity-80 font-medium`}>
                  No purchases yet
                </p>
                <p className={`text-xs ${theme.textSecondary} opacity-60 mt-1`}>
                  Add your first purchase above to get started
                </p>
              </div>
            )}
          </div>
        </div>
      </div>





      {/* Purchase history table */}
      {state.purchases.length > 0 && (
        <div className={`${theme.card} rounded-2xl shadow-lg border ${theme.border} ${getCardBackground('bg-gradient-to-br from-slate-50 via-gray-50 to-zinc-50', 'bg-gray-800/50')}`}>
          <div className={`px-8 py-6 border-b ${theme.border} ${getCardBackground('bg-white/60', 'bg-gray-700/50')} backdrop-blur-sm rounded-t-2xl`}>
            <h2 className={`text-2xl font-bold ${theme.text} flex items-center gap-3`}>
              <div className="p-3 rounded-2xl bg-gradient-to-br from-slate-500 to-gray-600 shadow-lg">
                <HiCalendar className="h-6 w-6 text-white" />
              </div>
              All Purchases
            </h2>
            <p className={`mt-2 ${theme.textSecondary} opacity-80`}>
              Complete history of all your purchase transactions
            </p>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className={theme.secondary}>
                <tr>
                  <th className={`px-6 py-3 text-left text-xs font-medium ${theme.textSecondary} uppercase tracking-wider`}>
                    Date & Time
                  </th>
                  <th className={`px-6 py-3 text-left text-xs font-medium ${theme.textSecondary} uppercase tracking-wider`}>
                    Amount ({state.currencySymbol || 'R'})
                  </th>
                  <th className={`px-6 py-3 text-left text-xs font-medium ${theme.textSecondary} uppercase tracking-wider`}>
                    {getDisplayUnitName()}
                  </th>
                  <th className={`px-6 py-3 text-left text-xs font-medium ${theme.textSecondary} uppercase tracking-wider`}>
                    Cost per {getDisplayUnitName()}
                  </th>
                </tr>
              </thead>
              <tbody className={`${theme.card} divide-y divide-gray-200`}>
                {state.purchases.map((purchase) => (
                  <tr key={purchase.id}>
                    <td className={`px-6 py-4 whitespace-nowrap text-sm ${theme.text}`}>
                      {purchase.timestamp}
                    </td>
                    <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium ${theme.text}`}>
                      {state.currencySymbol || 'R'}{purchase.currency.toFixed(2)}
                    </td>
                    <td className={`px-6 py-4 whitespace-nowrap text-sm ${theme.text}`}>
                      {purchase.units.toFixed(2)}
                    </td>
                    <td className={`px-6 py-4 whitespace-nowrap text-sm ${theme.text}`}>
                      {state.currencySymbol || 'R'}{purchase.unitCost.toFixed(2)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  )
}

export default Purchases
