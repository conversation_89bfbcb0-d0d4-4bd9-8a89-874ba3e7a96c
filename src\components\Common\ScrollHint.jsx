import React, { useState, useEffect } from 'react'
import { useTheme } from '../../context/ThemeContext'

function ScrollHint({ children, className = "" }) {
  const { theme } = useTheme()
  const [showHint, setShowHint] = useState(true)

  // Hide hint after user scrolls or after 3 seconds
  useEffect(() => {
    const timer = setTimeout(() => {
      setShowHint(false)
    }, 3000)

    return () => clearTimeout(timer)
  }, [])

  const handleScroll = () => {
    setShowHint(false)
  }

  return (
    <div className={`relative ${className}`}>
      {/* Mobile scroll hint */}
      {showHint && (
        <div className="md:hidden absolute top-2 right-2 z-10 pointer-events-none">
          <div className={`text-xs ${theme.textSecondary} opacity-60 flex items-center gap-1 bg-black/10 px-2 py-1 rounded-full backdrop-blur-sm animate-pulse`}>
            <span>Swipe →</span>
          </div>
        </div>
      )}
      
      <div 
        className="overflow-x-auto overflow-y-hidden" 
        style={{ WebkitOverflowScrolling: 'touch' }}
        onScroll={handleScroll}
      >
        {children}
      </div>
    </div>
  )
}

export default ScrollHint
