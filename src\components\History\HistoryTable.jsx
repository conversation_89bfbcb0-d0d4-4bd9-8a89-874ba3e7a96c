import React from 'react'
import { useApp } from '../../context/AppContext'
import { useTheme } from '../../context/ThemeContext'
import { HiCurrencyDollar, HiTrendingUp, HiArrowUp, HiArrowDown } from 'react-icons/hi'

function HistoryTable({ history }) {
  const { state, getDisplayUnitName } = useApp()
  const { theme, currentTheme } = useTheme()

  if (history.length === 0) {
    return null
  }

  return (
    <div className="overflow-x-auto">
      <table className={`min-w-full divide-y ${currentTheme === 'dark' ? 'divide-gray-600' : 'divide-gray-200'}`}>
        <thead className={theme.secondary}>
          <tr>
            <th className={`px-6 py-3 text-left text-xs font-medium ${theme.textSecondary} uppercase tracking-wider`}>
              Type
            </th>
            <th className={`px-6 py-3 text-left text-xs font-medium ${theme.textSecondary} uppercase tracking-wider`}>
              Date & Time
            </th>
            <th className={`px-6 py-3 text-left text-xs font-medium ${theme.textSecondary} uppercase tracking-wider`}>
              Details
            </th>
            <th className={`px-6 py-3 text-left text-xs font-medium ${theme.textSecondary} uppercase tracking-wider`}>
              {getDisplayUnitName()}
            </th>
            <th className={`px-6 py-3 text-left text-xs font-medium ${theme.textSecondary} uppercase tracking-wider`}>
              Amount
            </th>
          </tr>
        </thead>
        <tbody className={`${theme.card} divide-y ${currentTheme === 'dark' ? 'divide-gray-600' : 'divide-gray-200'}`}>
          {history.map((item) => (
            <tr key={`${item.type}-${item.id}`} className={`${currentTheme === 'dark' ? 'hover:bg-gray-700' : 'hover:bg-gray-50'} transition-colors duration-200`}>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center">
                  {item.type === 'purchase' ? (
                    <div className="flex items-center">
                      <div className={`p-2 rounded-lg ${theme.secondary}`}>
                        <HiCurrencyDollar className={`h-4 w-4 ${theme.textSecondary}`} />
                      </div>
                      <div className="ml-3">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${currentTheme === 'dark' ? 'bg-green-900/30 text-green-400' : 'bg-green-100 text-green-800'}`}>
                          <HiArrowUp className="mr-1 h-3 w-3" />
                          Purchase
                        </span>
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-center">
                      <div className={`p-2 rounded-lg ${currentTheme === 'dark' ? 'bg-red-900/30' : 'bg-red-100'}`}>
                        <HiTrendingUp className="h-4 w-4 text-red-600" />
                      </div>
                      <div className="ml-3">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${currentTheme === 'dark' ? 'bg-red-900/30 text-red-400' : 'bg-red-100 text-red-800'}`}>
                          <HiArrowDown className="mr-1 h-3 w-3" />
                          Usage
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              </td>
              
              <td className={`px-6 py-4 whitespace-nowrap text-sm ${theme.text}`}>
                {item.timestamp}
              </td>
              
              <td className={`px-6 py-4 text-sm ${theme.text}`}>
                {item.type === 'purchase' ? (
                  <div>
                    <p className="font-medium">Electricity Purchase</p>
                    <p className={`text-xs ${theme.textSecondary}`}>
                      @ {state.currencySymbol || 'R'}{item.unitCost.toFixed(2)} per {getDisplayUnitName()}
                    </p>
                  </div>
                ) : (
                  <div>
                    <p className="font-medium">Usage Recording</p>
                    <p className={`text-xs ${theme.textSecondary}`}>
                      From {item.previousUnits.toFixed(2)} to {item.currentUnits.toFixed(2)} {getDisplayUnitName()}
                    </p>
                  </div>
                )}
              </td>
              
              <td className={`px-6 py-4 whitespace-nowrap text-sm ${theme.text}`}>
                {item.type === 'purchase' ? (
                  <span className="text-green-600 font-medium">
                    +{item.units.toFixed(2)}
                  </span>
                ) : (
                  <span className="text-red-600 font-medium">
                    -{item.usage.toFixed(2)}
                  </span>
                )}
              </td>
              
              <td className={`px-6 py-4 whitespace-nowrap text-sm ${theme.text}`}>
                {item.type === 'purchase' ? (
                  <span className="text-green-600 font-medium">
                    +{state.currencySymbol || 'R'}{item.currency.toFixed(2)}
                  </span>
                ) : (
                  <span className="text-red-600 font-medium">
                    -{state.currencySymbol || 'R'}{(item.usage * state.unitCost).toFixed(2)}
                  </span>
                )}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}

export default HistoryTable
